import React from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { selectRequests } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import { IoEyeSharp } from "react-icons/io5";
import { LiaComment } from "react-icons/lia";
import Table from "../../components/common/Table";
import "../../styles/SellerRequests.css";

const SellerRequests = () => {
  const requests = useSelector(selectRequests);
  const navigate = useNavigate();

  const handleViewDetails = (requestId) => {
    navigate(`/seller/request-details/${requestId.replace('#', '')}`);
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    { key: "id", label: "Request Id" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (item) => (
        <div className="video-doc">
          <img src={item.image} alt={item.title} />
          <span>{item.title}</span>
        </div>
      ),
    },
    { key: "date", label: "Date" },
    { key: "price", label: "Price" },
    { key: "requestedAmount", label: "Requested Amount" },
    { key: "requestedCustomer", label: "Requested Customer" },
    {
      key: "action",
      label: "Action",
      render: (item) => (
        <div className="action-icons">
          <IoEyeSharp
            className="action-icon"
            onClick={() => handleViewDetails(item.id)}
          />
          <LiaComment className="action-icon" />
        </div>
      ),
    },
  ];

  const formatData = (requests) => {
    return requests.map((item, index) => ({
      ...item,
      no: index + 1,
      date: `${item.date} | 4:50PM`,
    }));
  };

  return (
    <SellerLayout>
      <div className="seller-requests-container">
        <Table
          columns={columns}
          data={formatData(requests)}
          className="requests-table"
        />
      </div>
    </SellerLayout>
  );
};

export default SellerRequests;
