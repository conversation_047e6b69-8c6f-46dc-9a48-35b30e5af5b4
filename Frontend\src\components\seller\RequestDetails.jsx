import React from "react";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { selectRequests } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "./SellerLayout";
import Table from "../common/Table";
import "../../styles/RequestDetails.css";

const getRequestDetails = (requestId, requests) => {
  const foundRequest = requests.find(req => req.id === `#${requestId}`);
  
  if (foundRequest) {
    return {
      ...foundRequest,
      time: "4:50PM",
      customer: foundRequest.customer || {
        name: foundRequest.requestedCustomer,
        email: `${foundRequest.requestedCustomer.toLowerCase().replace(' ', '.')}@email.com`,
        phone: "************"
      },
      history: [
        {
          no: 1,
          requestId: `#${requestId}`,
          requestedCustomer: foundRequest.requestedCustomer,
          date: foundRequest.date,
          price: foundRequest.price,
          requestedAmount: foundRequest.requestedAmount,
          status: "Pending",
          offerAmount: "-"
        },
        {
          no: 2,
          requestId: `#${requestId}`,
          requestedCustomer: foundRequest.requestedCustomer,
          date: foundRequest.date,
          price: foundRequest.price,
          requestedAmount: foundRequest.requestedAmount,
          status: "Accepted",
          offerAmount: "-"
        },
        {
          no: 3,
          requestId: `#${requestId}`,
          requestedCustomer: foundRequest.requestedCustomer,
          date: foundRequest.date,
          price: foundRequest.price,
          requestedAmount: foundRequest.requestedAmount,
          status: "Counter Offer",
          offerAmount: "$17.00"
        }
      ]
    };
  }
  
  return {
    id: `#${requestId}`,
    title: "Frank Martin - Drills and Coaching Philosophies to Developing Toughness in Your Players",
    subtitle: "Basketball Coaching Clinic",
    date: "20 May 2025",
    time: "4:50PM",
    price: "$22.00",
    requestedAmount: "$19.00",
    image: "https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop",
    customer: {
      name: "John Smith",
      email: "<EMAIL>",
      phone: "************"
    },
    history: [
      {
        no: 1,
        requestId: `#${requestId}`,
        requestedCustomer: "John Smith",
        date: "20 May 2025",
        price: "$22.00",
        requestedAmount: "$19.00",
        status: "Pending",
        offerAmount: "-"
      }
    ]
  };
};

const RequestDetails = () => {
  const { id } = useParams();
  const requests = useSelector(selectRequests);
  const request = getRequestDetails(id, requests);

  const historyColumns = [
    { key: "no", label: "No." },
    { key: "requestId", label: "Request Id" },
    { key: "requestedCustomer", label: "Requested Customer" },
    { key: "date", label: "Date" },
    { key: "price", label: "Price" },
    { key: "requestedAmount", label: "Requested Amount" },
    { 
      key: "status", 
      label: "Status",
      render: (item) => (
        <span className={`status-badge status-${item.status.toLowerCase().replace(' ', '-')}`}>
          {item.status}
        </span>
      )
    },
    { key: "offerAmount", label: "Offer Amount" },
    {
      key: "action",
      label: "Action",
      render: () => (
        <div className="action-icons">
          <button className="action-btn view-btn">👁</button>
          <button className="action-btn edit-btn">✏️</button>
        </div>
      )
    }
  ];

  return (
    <SellerLayout>
      <div className="RequestDetails">
        <div className="RequestDetails__content">
          <div className="RequestDetails__main-section">
            <div className="RequestDetails__header">
              <div className="RequestDetails__content-info">
                <img 
                  src={request.image} 
                  alt={request.title}
                  className="RequestDetails__content-image"
                />
                <div className="RequestDetails__content-details">
                  <h3 className="RequestDetails__content-title">{request.title}</h3>
                  <p className="RequestDetails__content-subtitle">{request.subtitle}</p>
                </div>
              </div>
            </div>

            <div className="RequestDetails__info-grid">
              <div className="RequestDetails__info-section">
                <h3 className="RequestDetails__section-title">Request Information</h3>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Request Id:</span>
                  <span className="RequestDetails__info-value">{request.id}</span>
                </div>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Date:</span>
                  <span className="RequestDetails__info-value">{request.date} | {request.time}</span>
                </div>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Price:</span>
                  <span className="RequestDetails__info-value">{request.price}</span>
                </div>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Requested Amount:</span>
                  <span className="RequestDetails__info-value">{request.requestedAmount}</span>
                </div>
              </div>

              <div className="RequestDetails__customer-section">
                <h3 className="RequestDetails__section-title">Customer Details</h3>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Name:</span>
                  <span className="RequestDetails__info-value">{request.customer.name}</span>
                </div>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Email Address:</span>
                  <span className="RequestDetails__info-value">{request.customer.email}</span>
                </div>
                <div className="RequestDetails__info-item">
                  <span className="RequestDetails__info-label">Phone Number:</span>
                  <span className="RequestDetails__info-value">{request.customer.phone}</span>
                </div>
                
                
              </div>
            </div>
          </div>
<div className="RequestDetails__actions">
                  <button className="RequestDetails__btn RequestDetails__btn--accept">
                    Accepted
                  </button>
                  <button className="RequestDetails__btn btn-primary">
                    Rejected
                  </button>
                  <button className=" btn-outline">
                    Counter Offer
                  </button>
                 
                </div>
          <div className="RequestDetails__history-section">
            <h3 className="RequestDetails__section-title">History</h3>
            <Table
              columns={historyColumns}
              data={request.history}
              className="RequestDetails__history-table"
            />
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default RequestDetails;
